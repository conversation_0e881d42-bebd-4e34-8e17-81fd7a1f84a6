const {
  getScheduledMessages,
  removeMessage,
  removeAllMessages,
  checkPendingMessages,
} = require("../utils/redisClient");
const { FirestoreRef, CONSTANTS, COLLECTIONS } = require("../init");
const { momentNow } = require("../helpers");
const moment = require("moment");
const axios = require("axios");
const dotenv = require("dotenv");
const { sendEmail } = require("../resend");
const { message } = require("../models/childnodes/deadline");
const { MOMENT_ISO, APP_TRIGGERS } = require("../constants");
const { addNewLog } = require("../post");

// Carregar variáveis de ambiente do arquivo .env
dotenv.config();

/**
 * Processa mensagens agendadas do Redis e envia para o chat-api
 * @param {Object} options - Opções de processamento
 * @param {number} options.batchSize - Número máximo de mensagens a processar por vez (default: 50)
 * @param {boolean} options.dryRun - Se true, não envia as mensagens, apenas simula (default: true)
 * @param {boolean} options.simulateOnly - Se true, apenas simula o envio com logs detalhados (default: true)
 * @returns {Promise<Array>} Lista de mensagens processadas
 */
const sendEmails = async (options = {}) => {
  try {
    // Processar as mensagens agendadas
    const processedMessages = await processScheduledEmails(options);

    console.log(
      `EMAILCRON > SHOTX SEND MESSAGES > Processadas ${processedMessages.length} mensagens`
    );

    return processedMessages;
  } catch (error) {
    console.error(
      "EMAILCRON > SHOTX SEND MESSAGES > Erro no processamento:",
      error.message
    );
    console.error(error.stack);
    return [];
  }
};
const processScheduledEmails = async (options = {}) => {
  // Definir opções padrão - simulação ativada por padrão
  const {
    batchSize = 50,
    dryRun = false, // Alterado para true por padrão
    simulateOnly = false, // Nova opção para apenas simular o envio
  } = options;

  console.log(
    `EMAILCRON > PROCESS MESSAGES > START > Modo: ${simulateOnly ? "SIMULAÇÃO" : "PRODUÇÃO"}, DryRun: ${dryRun ? "SIM" : "NÃO"}`
  );

  try {
    const scheduledListKey = "email:scheduled_emails";

    let momentNowISO = momentNow().format(CONSTANTS.MOMENT_ISO);
    let momentNowInstanceTimestamp = new Date(momentNowISO).getTime();
    console.log(
      `EMAILCRON > PROCESS MESSAGES > START > SCORE: ${momentNowInstanceTimestamp}`
    );
    const messages = await getScheduledMessages(
      scheduledListKey,
      momentNowInstanceTimestamp,
      "PROCESS",
      {
        limit: batchSize,
        remove: false,
      }
    );

    if (messages.length === 0) {
      console.log("EMAILCRON > PROCESS MESSAGES > NO MESSAGES");
      return [];
    }

    const totalMessages = messages.length;
    console.log(
      `EMAILCRON > PROCESS MESSAGES > TOTAL MESSAGES ${totalMessages} TO PROCESS`
    );

    let messageIndex = 0;
    let messagesProcesseds = [];
    let logs = [];

    const messagesProcess = messages.map(async (message) => {
      const logData = {
        mailId: message.context.id,

        contactId: message.contactId,
      };

      const log = {
        user_id: message.contactId,
        contactId: message.contactId,
        operator_id: 0,
        id: message.context.id || "",
        collection: message.context.collection || "",
        trigger: "",
        date: momentNow().format(MOMENT_ISO),
        owner: message.owner,
        accountId: message.accountId,
        data: logData,
        context: message.context,
      };
      try {
        const messageId =
          message.id ||
          `msg_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

        if (dryRun) {
          console.log(
            `EMAILCRON > PROCESS MESSAGES > [DRY RUN] Would send message ${messageId}`
          );

          //   // Atualizar status no Firestore
          //   await FirestoreRef.collection("shotx_messages_sent")
          //     .doc(messageId)
          //     .update({
          //       status: "simulated",
          //       _processed_at: momentNow().format(CONSTANTS.MOMENT_ISO),
          //     });

          // Usar a redis_key da mensagem para remoção correta
          if (message.redis_key) {
            await removeMessage(message.redis_key, "shotx:scheduled_messages");
          }
          return { ...message, id: messageId, status: "simulated" };
        }

        messagesProcesseds.push(message);

        messageIndex += 1;

        if (messageIndex === totalMessages) {
          // Verificar status atual do email no Firestore antes de atualizar
          try {
            console.log(
              `EMAILCRON > STATUS CHECK > Verificando status do email ${message.ID} antes de iniciar envio`
            );

            const emailDoc = await FirestoreRef.collection(
              COLLECTIONS.MAIL_COLLECTION_NAME
            )
              .doc(message.ID)
              .get();

            if (emailDoc.exists) {
              const emailData = emailDoc.data();
              const currentSent = emailData.sent;
              const currentPrepared = emailData.sending;

              console.log(
                `EMAILCRON > STATUS CHECK > Email ${message.ID} status:`,
                { sent: currentSent, prepared: currentPrepared }
              );

              if (currentSent === false && currentPrepared === false) {
                console.log(
                  `EMAILCRON > SENDINGUPDATE > Atualizando status para sending=true, prepared=false`
                );

                await FirestoreRef.collection(COLLECTIONS.MAIL_COLLECTION_NAME)
                  .doc(message.ID)
                  .update({
                    sending: true,
                    prepared: false,
                  });
              }
            } else {
              console.error(
                `EMAILCRON > STATUS CHECK > Email ${message.ID} não encontrado no Firestore`
              );
            }
          } catch (error) {
            console.error(
              `EMAILCRON > STATUS CHECK > Erro ao verificar status do email ${message.ID}:`,
              error.message
            );
          }

          if (message.hasError) {
            console.warn(
              `EMAILCRON > PROCESS MESSAGES > EMAIL INVALIDO > KEY:`,
              message.redis_key
            );
            logs.push({ ...log, trigger: APP_TRIGGERS.APP_TRIGGER_FAILED });
            removeMessage(message.redis_key, scheduledListKey);
          }

          await sendEmail(message, async (result) => {
            if (result && !result.hasError) {
              logs.push({ ...log, trigger: APP_TRIGGERS.APP_TRIGGER_SENT });
              await removeMessage(message.redis_key, scheduledListKey);
              console.log(
                `EMAILCRON > SENDMESSAGE > ENVIO BEM-SUCEDIDO > KEY:`,
                message.redis_key
              );
            } else {
              console.warn(
                `EMAILCRON > SENDMESSAGE > FALHA NO ENVIO - EMAIL REMOVIDO DA FILA > MESSAGE:`,
                message,
                "KEY:",
                message.redis_key
              );
              logs.push({ ...log, trigger: APP_TRIGGERS.APP_TRIGGER_FAILED });
              removeMessage(message.redis_key, scheduledListKey);
            }
          });
        }
        console.log("EMAILCRON > ALLLOGS", logs.length);
        checkPendingMessages(message.context.id, message.accountId)
          .then(async (checkMessages) => {
            console.log("EMAILCRON > CHECK MESSAGES", checkMessages);
            if (checkMessages === false) {
              await FirestoreRef.collection(COLLECTIONS.MAIL_COLLECTION_NAME)
                .doc(message.ID)
                .update({
                  sent: true,
                  sending: false,
                });
            }
            return checkMessages;
          })
          .catch((error) => {
            console.error(
              "EMAILCRON > ERROR CHECKING PENDING MESSAGES:",
              error
            );
          });
        // console.log("EMAILCRON > CHECK MESSAGES", checkMessages);
        // if (checkMessages === false) {
        //   await FirestoreRef.collection(COLLECTIONS.MAIL_COLLECTION_NAME)
        //     .doc(message.ID)
        //     .update({
        //       sent: true,
        //       sending: false,
        //     });
        // }
        // logs.map((l) => addNewLog(l));
        return 0;
      } catch (error) {
        console.error(
          `EMAILCRON > ERROR TO SEND MESSAGES > ERROR:`,
          error.message
        );
        await FirestoreRef.collection(COLLECTIONS.MAIL_COLLECTION_NAME)
          .doc(message.ID)
          .update({
            error: true,
            sent: false,
            prepared: false,
            sending: false,
          });
        return null;
      }
    });

    return messagesProcess;
  } catch (error) {
    console.error(
      "EMAILCRON > PROCESS MESSAGES > Fatal error during processing:",
      error.message
    );
    console.error(error.stack);
    return [];
  }
};

/**
 * Limpa todas as mensagens agendadas do Redis
 * @returns {Promise<number>} - Número de mensagens removidas
 */
const clearAllMessages = async () => {
  try {
    console.log(
      "EMAILCRON > CLEAR ALL MESSAGES > Iniciando limpeza de mensagens"
    );

    // Chave da lista ordenada de mensagens agendadas
    const scheduledListKey = "email:scheduled_emails";

    // Remover todas as mensagens
    const removedCount = await removeAllMessages(scheduledListKey);

    console.log(
      `EMAILCRON > CLEAR ALL MESSAGES > Removidas ${removedCount} mensagens`
    );

    return removedCount;
  } catch (error) {
    console.error("EMAILCRON > CLEAR ALL MESSAGES > ERROR:", error);
    return 0;
  }
};

/**
 * Remove uma mensagem específica pelo ID
 * @param {string} messageId - ID da mensagem a ser removida
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const deleteMessage = async (messageId) => {
  try {
    if (!messageId) {
      console.error(
        "EMAILCRON > DELETE MESSAGE > ID da mensagem é obrigatório"
      );
      return false;
    }

    // Formatar a chave da mensagem
    const messageKey = messageId.startsWith("shotx:")
      ? messageId
      : `shotx:message:${messageId}`;

    // Chave da lista ordenada de mensagens agendadas
    const scheduledListKey = "shotx:scheduled_messages";

    // Remover a mensagem
    const success = await removeMessage(messageKey, scheduledListKey);

    if (success) {
      console.log(
        `EMAILCRON > DELETE MESSAGE > Mensagem ${messageId} removida com sucesso`
      );
    } else {
      console.error(
        `EMAILCRON > DELETE MESSAGE > Falha ao remover mensagem ${messageId}`
      );
    }

    return success;
  } catch (error) {
    console.error(`EMAILCRON > DELETE MESSAGE > ERROR:`, error);
    return false;
  }
};

module.exports = {
  sendEmails,
};
