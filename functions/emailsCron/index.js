const { MOMENT_ISO } = require("../constants");
const {
  FirestoreRef,
  ERROR_TYPES,
  functions,
  COLLECTIONS,
  momentNow,
} = require("../init");
const { validateMailbox } = require("../mailgun");
const { sendMail } = require("../mailing");
const { sendEmail } = require("../resend");
const { prepareEmailToSaveinRedis } = require("./prepareEmail");
const emailsCron = async () => {
  console.log("EMAILCRON > START");

  let now = momentNow().format(MOMENT_ISO);
  console.log("EMAILCRON > now", now);

  const emails = [];

  try {
    await FirestoreRef.collection(COLLECTIONS.MAIL_COLLECTION_NAME)
      .where("error", "==", false)
      .where("sent", "==", false)
      .where("sending", "==", false)
      .where("prepared", "==", false)
      .where("scheduled_date", "<=", now)
      .get()
      .then((snapshot) => {
        console.log("EMAILCRON > snapshot.size", snapshot.size);

        if (!snapshot.empty) {
          snapshot.forEach((doc) => {
            const data = doc.data();
            console.log(
              "EMAILCRON > processing doc",
              doc.id,
              "scheduled_date:",
              data.scheduled_date
            );
            prepareEmailToSaveinRedis(data);
            emails.push(data);
          });
        }
        return snapshot;
      })
      .catch((error) => {
        console.error("EMAILCRON > Firestore query error:", error);
        throw error;
      });
  } catch (error) {
    console.error("EMAILCRON > Error:", error);
    return [];
  }

  console.log("EMAILCRON > emails.length", emails.length);
  return emails;
};

module.exports = {
  emailsCron,
};
