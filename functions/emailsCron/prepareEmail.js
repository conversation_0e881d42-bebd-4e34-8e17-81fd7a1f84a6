const { MOMENT_ISO, APP_TRIGGERS } = require("../constants");
const { COLLECTIONS, FirestoreRef, momentNow } = require("../init");
const { email } = require("../models/leadWebhook");
const { getLeadById, addNewLog } = require("../post");
const { sendEmail, validateMailbox } = require("../resend");
const { replaceShortCodes } = require("../shortcodes");
const { saveMessageInRedis } = require("../shotxOrganize");
const {
  saveMessage,
  saveScheduledMessage,
  removeAllMessages,
} = require("../utils/redisClient");

async function prepareEmailToSaveinRedis(email) {
  const { contacts } = email;
  console.log("EMAILCRON > PREPARE EMAIL > EMAIL", email);
  if ((!contacts || !contacts.length) && !email.segmentations) {
    console.log("EMAILCRON > PREPARE EMAIL > NO CONTACTS");
    return;
  }
  let index = 1;
  let leadsWithError = 0;
  for (const contactId of contacts) {
    const emailPrepared = await prepareEmailForContact(
      contactId,
      email,
      contacts.length,
      index
    );
    if (emailPrepared.isValid === false) {
      console.log("EMAILCRON > PREPARED EMAIL > EMAILPREPARED > ELSE");
      leadsWithError++;
      if (leadsWithError === contacts.length) {
        console.log("EMAILCRON > ALL LEADS WITH ERROR");
        await FirestoreRef.collection(COLLECTIONS.MAIL_COLLECTION_NAME)
          .doc(email.ID)
          .update({
            prepared: false,
            error: true,
          });
      }
    }

    if (emailPrepared) {
      console.log("EMAILCRON > PREPARED EMAIL > EMAILPREPARED", emailPrepared);
      console.log("EMAILCRON > PREPARED EMAIL > EMAILPREPARED > Index", index);
      saveEmailInRedis(emailPrepared);
    }
    index++;
  }

  // await FirestoreRef.collection(COLLECTIONS.MAIL_COLLECTION_NAME)
  //   .doc(email.ID)
  //   .update({
  //     prepared: true,
  //   });
}

async function prepareEmailForContact(
  contactId,
  email,
  qtdAllLeads,
  index,
  qtdErrors
) {
  const lead = await getLeadById(contactId);

  const hasError = qtdErrors > 0 ? true : false;

  if (!lead) {
    console.error(
      `EMAILCRON > PREPARE EMAIL > Lead not found for contact ID: ${contactId}`
    );
    return null;
  }

  const emailIsValid = await validateMailbox(lead.email, (result) => {
    console.log(
      "EMAILCRON > PREPARE EMAIL > LEAD EMAIL",
      lead.email,
      "ISVALID:",
      result.body.is_valid
    );
    return result;
  });

  const messageContent = await replaceShortCodes(
    email.html,
    [],
    COLLECTIONS.LEADS_COLLECTION_NAME,
    [lead]
  );

  if (lead.email) {
    const baseTimestamp = new Date(email.scheduled_date).getTime();

    const score = baseTimestamp + index * 60000;
    console.log("EMAILCRON > PREPARED EMAIL > SCORE", score);
    const emailIdToSaveInRedis = `email:${email.ID}_${lead.ID}_${score}`;

    const emailPrepared = {
      ...email,
      html: messageContent.content,
      leadId: contactId,
      leadData: {
        name: lead.displayName || lead.name || "",
        email: lead.email || "",
        phone: lead.mobile || "",
      },
      to: lead.email,
      qtdLeads: qtdAllLeads,
      index: index,
      hasError: hasError,
      qtdErrors: qtdErrors,
      isValid: emailIsValid.body.is_valid,
      score: score,
      redisKey: emailIdToSaveInRedis,
    };
    console.log("EMAILCRON > PREPARED EMAIL", emailPrepared);
    return emailPrepared;
  }
  console.log("EMAILCRON > PREPARED EMAIL > NO EMAIL ON LEAD");
  return null;
}

async function saveEmailInRedis(emailPrepared) {
  const emailList = "email:scheduled_emails";
  const emailWithMetadata = {
    ...emailPrepared,
    redis_key: emailPrepared.redisKey,
    _scheduled_timestamp: emailPrepared.score,
    _scheduled_iso: emailPrepared.scheduled_date,
    _created_at: new Date().toISOString(),
  };

  console.log("EMAILCRON > SAVE EMAIL IN REDIS > SCORE WITH INDEX", {
    emailWithMetadata,
    originalDate: emailPrepared.scheduled_date,
    index: emailPrepared.index,
    calculatedScore: emailPrepared.score,
    scoreDate: new Date(emailPrepared.score).toISOString(),
  });

  // Chamar saveScheduledMessage com os parâmetros corretos:
  // (listKey, score, messageKey, message)
  const emailSaved = await saveScheduledMessage(
    emailList, // listKey
    emailPrepared.score, // score (timestamp + index * 1000)
    emailPrepared.redisKey, // messageKey
    emailWithMetadata // message
  );

  return emailSaved;
}

module.exports = {
  prepareEmailToSaveinRedis,
};
