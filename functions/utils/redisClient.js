const redis = require("redis");
const dotenv = require("dotenv");
const functions = require("firebase-functions");

// Carregar variáveis de ambiente do arquivo .env
dotenv.config();

/**
 * Configurações do Redis
 * Utilizando variáveis de ambiente para maior segurança e flexibilidade
 */
const REDIS_CONFIG = {
  socket: {
    host: functions.config().redis.host || "localhost", // Host do Redis
    port: parseInt(functions.config().redis.port || "6379"), // Porta padrão do Redis
    connectTimeout: parseInt("10000"), // Timeout de conexão em ms (10 segundos)
    reconnectStrategy: (retries) => {
      // Estratégia de reconexão com backoff exponencial
      const delay = Math.min(
        // Começar com 100ms, dobrar a cada tentativa, máximo de 10 segundos
        Math.pow(2, retries) * 100,
        10000
      );
      console.log(
        `SHOTXCRON > Redis reconnection attempt ${retries + 1}, delay: ${delay}ms`
      );
      return delay;
    },
  },
  password: functions.config().redis.password || "", // Senha para autenticação no Redis
  database: parseInt(functions.config().redis.database || "0"), // Banco de dados padrão
  // Configurações adicionais para melhorar a estabilidade
  disableOfflineQueue: false, // Permitir enfileirar comandos quando desconectado
  enableReadyCheck: true, // Verificar se o Redis está pronto antes de aceitar comandos
  maxRetriesPerRequest: 3, // Número máximo de tentativas por comando
};

// Variável para armazenar a instância do cliente Redis
let redisClient = null;

/**
 * Inicializa e retorna um cliente Redis com tratamento de erros e reconexão
 * @returns {Promise<Object|null>} Cliente Redis ou null em caso de erro
 */
const getRedisClient = async () => {
  // Se já existe um cliente conectado, retorná-lo
  if (redisClient && redisClient.isOpen) {
    return redisClient;
  }

  // Se existe um cliente mas não está conectado, tentar reconectar
  if (redisClient && !redisClient.isOpen) {
    try {
      console.log(
        "SHOTXCRON > REDIS > Attempting to reconnect existing client"
      );
      await redisClient.connect();
      return redisClient;
    } catch (error) {
      console.error(
        "SHOTXCRON > REDIS > Failed to reconnect existing client:",
        error.message
      );
      // Continuar para criar um novo cliente
      redisClient = null;
    }
  }

  try {
    console.log(
      `SHOTXCRON > REDIS > Creating new connection to ${REDIS_CONFIG.socket.host}:${REDIS_CONFIG.socket.port}`
    );

    // Criar um novo cliente Redis com as configurações definidas
    redisClient = redis.createClient(REDIS_CONFIG);

    // Configurar handlers de eventos para melhor observabilidade
    redisClient.on("error", (error) => {
      console.error("SHOTXCRON > REDIS > Error:", error.message);

      // Tratamento específico para erros comuns
      if (error.message.includes("NOAUTH")) {
        console.error(
          "SHOTXCRON > REDIS > Authentication Error: Check password configuration"
        );
      } else if (error.message.includes("ECONNREFUSED")) {
        console.error(
          "SHOTXCRON > REDIS > Connection Error: Server unreachable"
        );
      } else if (error.message.includes("ETIMEDOUT")) {
        console.error(
          "SHOTXCRON > REDIS > Timeout Error: Connection timed out"
        );
      }
    });

    redisClient.on("connect", () => {
      console.log("SHOTXCRON > REDIS > Connected successfully");
    });

    redisClient.on("ready", () => {
      console.log("SHOTXCRON > REDIS > Client authenticated and ready to use");
    });

    redisClient.on("reconnecting", () => {
      console.log("SHOTXCRON > REDIS > Attempting to reconnect...");
    });

    redisClient.on("end", () => {
      console.log("SHOTXCRON > REDIS > Connection closed");
      redisClient = null;
    });

    // Conectar ao servidor Redis
    await redisClient.connect();

    // Verificar se a conexão está realmente estabelecida
    if (!redisClient.isOpen) {
      throw new Error("Failed to establish connection");
    }

    console.log("SHOTXCRON > REDIS > Connection established and ready");
    return redisClient;
  } catch (error) {
    console.error(
      "SHOTXCRON > REDIS > Failed to create client:",
      error.message
    );
    console.error(error.stack);

    // Limpar a referência para permitir nova tentativa
    redisClient = null;
    return null;
  }
};

/**
 * Salva uma mensagem no Redis com um tempo de expiração
 * @param {string} key - Chave para armazenar a mensagem
 * @param {Object} message - Objeto da mensagem a ser armazenado
 * @param {number} expireInSeconds - Tempo de expiração em segundos (opcional)
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const saveMessage = async (key, message, expireInSeconds = null) => {
  try {
    const client = await getRedisClient();
    if (!client) {
      console.error("SHOTXCRON > Redis client not available");
      return false;
    }

    // Converter o objeto para string JSON
    const messageStr = JSON.stringify(message);

    // Salvar no Redis (API moderna usa apenas .set())
    const options = {};
    if (expireInSeconds && !isNaN(expireInSeconds)) {
      options.EX = expireInSeconds;
    }

    await client.set(key, messageStr, options);

    return true;
  } catch (error) {
    console.error("SHOTXCRON > Error saving message to Redis:", error);
    return false;
  }
};

/**
 * Salva uma mensagem em uma lista ordenada por tempo no Redis
 * @param {string} listKey - Chave da lista ordenada
 * @param {number} score - Pontuação para ordenação (timestamp)
 * @param {string} messageKey - Chave única da mensagem
 * @param {Object} message - Objeto da mensagem a ser armazenado
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const saveScheduledMessage = async (listKey, score, messageKey, message) => {
  try {
    const client = await getRedisClient();
    if (!client) {
      console.error("EMAILCRON > SAVEREDIS > Redis client not available");
      return false;
    }

    // Converter o score (timestamp) para uma data legível
    const scoreDate = new Date(score).toISOString();

    console.log(
      "EMAILCRON > SAVEREDIS > BEFORE SAVE",
      "key",
      messageKey,
      "message",
      message,
      "listkey",
      listKey,
      "score",
      score,
      "scoreDate",
      scoreDate
    );

    // Converter o objeto para string JSON
    const messageStr = JSON.stringify(message);

    // Salvar a mensagem com sua chave
    await client.set(messageKey, messageStr);

    // Adicionar à lista ordenada (API moderna usa .zAdd())
    await client.zAdd(listKey, [
      {
        score: score,
        value: messageKey,
      },
    ]);

    return true;
  } catch (error) {
    console.error(
      "EMAILCRON > SAVEREDIS > Error saving scheduled message to Redis:",
      error
    );
    return false;
  }
};

/**
 * Obtém mensagens agendadas até um determinado timestamp
 * @param {string} listKey - Chave da lista ordenada
 * @param {number} maxScore - Pontuação máxima (timestamp)
 * @param {Object} options - Opções adicionais
 * @param {boolean} options.remove - Se true, remove as mensagens da lista após obtê-las (default: true)
 * @param {number} options.limit - Número máximo de mensagens a retornar (default: 100)
 * @returns {Promise<Array>} - Lista de mensagens
 */
const getScheduledMessages = async (
  listKey,
  maxScore,
  origin = "",
  options = {}
) => {
  // Definir opções padrão
  const { remove = true, limit = 100 } = options;

  console.log("EMAILCRON > GETREDIS > START > ORIGIN", {
    origin,
  });

  try {
    // Obter cliente Redis
    const client = await getRedisClient();
    if (!client) {
      console.error(
        "SHOTXCRON > GETREDIS > Client not available for getting scheduled messages"
      );
      return [];
    }

    // Validar parâmetros
    if (!listKey) {
      console.error(
        "SHOTXCRON > GETREDIS > Missing list key for scheduled messages"
      );
      return [];
    }

    if (!maxScore || isNaN(maxScore)) {
      console.error(
        "SHOTXCRON > GETREDIS > Invalid max score for scheduled messages"
      );
      return [];
    }

    console.log(
      `SHOTXCRON > GETREDIS > SEARCHING FOR MESSAGES SCHEDULED UNTIL (${new Date(maxScore).toISOString()})`
    );

    // Obter chaves das mensagens até o timestamp especificado com limite
    const messageKeys = await client.zRange(listKey, 0, maxScore, {
      BY: "SCORE",
      LIMIT: {
        offset: 0,
        count: limit,
      },
    });

    // Se não houver mensagens, retornar array vazio
    if (!messageKeys || messageKeys.length === 0) {
      console.log("SHOTXCRON > GETREDIS > No scheduled messages found");
      return [];
    }

    console.log(
      `EMAILCRON > GETREDIS > Found ${messageKeys.length} scheduled messages to process`
    );

    // Obter as mensagens em lote usando pipeline para melhor performance
    const pipeline = client.multi();

    // Adicionar comandos GET para cada chave
    messageKeys.forEach((key) => {
      pipeline.get(key);
    });

    // Executar pipeline
    const messageResults = await pipeline.exec();

    // Processar resultados
    const messages = [];
    const failedKeys = [];
    const orphanedKeys = []; // Chaves que estão no sorted set mas não têm conteúdo

    console.log(
      `SHOTXCRON > GETREDIS > RESULTS > Processing ${messageResults.length} scheduled messages`
    );

    for (let i = 0; i < messageResults.length; i++) {
      const messageStr = messageResults[i];
      const key = messageKeys[i];

      if (messageStr) {
        try {
          const message = JSON.parse(messageStr);

          // Adicionar a chave Redis original à mensagem para facilitar a exclusão posterior
          message.redis_key = key;

          messages.push(message);

          console.log(
            `SHOTXCRON > GETREDIS > VALID MESSAGE > Key: ${key}, To: ${message.to || message.phone || "unknown"}`
          );
        } catch (e) {
          console.error(
            `SHOTXCRON > GETREDIS > Error parsing message with key ${key}:`,
            e.message
          );
          failedKeys.push(key);
        }
      } else {
        console.warn(
          `SHOTXCRON > GETREDIS > ORPHANED KEY > Key ${key} exists in sorted set but has no content`
        );
        orphanedKeys.push(key);
      }
    }

    // Limpar chaves órfãs (que estão no sorted set mas não têm conteúdo)
    if (orphanedKeys.length > 0) {
      console.log(
        `SHOTXCRON > GETREDIS > CLEANUP > Removing ${orphanedKeys.length} orphaned keys from sorted set`
      );

      try {
        await client.zRem(listKey, orphanedKeys);
        console.log(
          `SHOTXCRON > GETREDIS > CLEANUP > Successfully removed orphaned keys: ${orphanedKeys.join(", ")}`
        );
      } catch (cleanupError) {
        console.error(
          `SHOTXCRON > GETREDIS > CLEANUP > Error removing orphaned keys:`,
          cleanupError.message
        );
      }
    }

    console.log(
      `SHOTXCRON > GETREDIS > Successfully processed ${messages.length} messages`
    );
    return messages;
  } catch (error) {
    console.error(
      "SHOTXCRON > GETREDIS > Error getting scheduled messages:",
      error.message
    );
    console.error(error.stack);
    return [];
  }
};

/**
 * Remove uma mensagem específica do Redis
 * @param {string} messageKey - Chave da mensagem a ser removida
 * @param {string} listKey - Chave da lista ordenada (default: 'shotx:scheduled_messages')
 * @returns {Promise<boolean>} - Sucesso ou falha
 */
const removeMessage = async (
  messageKey,
  listKey = "shotx:scheduled_messages"
) => {
  try {
    const client = await getRedisClient();
    if (!client) {
      console.error(
        "SHOTXCRON > REDIS > Client not available for removing message"
      );
      return false;
    }

    console.log(
      `SHOTXCRON > REDIS > REMOVE > Attempting to remove message ${messageKey} from list ${listKey}`
    );

    // Usar transação para garantir atomicidade
    const multi = client.multi();

    // Verificar se a mensagem existe no sorted set
    const scoreResult = await client.zScore(listKey, messageKey);
    console.log(
      `SHOTXCRON > REDIS > REMOVE > Message ${messageKey} score in sorted set: ${scoreResult}`
    );

    // Verificar se a chave da mensagem existe
    const exists = await client.exists(messageKey);
    console.log(
      `SHOTXCRON > REDIS > REMOVE > Message key ${messageKey} exists: ${exists}`
    );

    if (!exists && scoreResult === null) {
      console.warn(
        `SHOTXCRON > REDIS > REMOVE > Message ${messageKey} not found in Redis (already removed)`
      );
      return true; // Considerar como sucesso se já foi removida
    }

    // Remover da lista ordenada (sorted set)
    multi.zRem(listKey, messageKey);

    // Remover o conteúdo da mensagem
    multi.del(messageKey);

    // Executar transação
    const results = await multi.exec();

    console.log(
      `SHOTXCRON > REDIS > REMOVE > Transaction results for ${messageKey}:`,
      results
    );

    // Verificar se ambas as operações foram bem-sucedidas
    // O Redis retorna arrays no formato [error, result] para cada comando
    const zRemResult = results[0];
    const delResult = results[1];

    // Extrair os valores dos resultados
    const zRemCount = zRemResult && zRemResult.length > 1 ? zRemResult[1] : 0;
    const delCount = delResult && delResult.length > 1 ? delResult[1] : 0;

    // Verificar se houve erros
    const zRemError = zRemResult && zRemResult[0];
    const delError = delResult && delResult[0];

    if (zRemError) {
      console.error(
        `SHOTXCRON > REDIS > REMOVE > Error in zRem for ${messageKey}:`,
        zRemError.message
      );
    }

    if (delError) {
      console.error(
        `SHOTXCRON > REDIS > REMOVE > Error in del for ${messageKey}:`,
        delError.message
      );
    }

    // Considerar sucesso se pelo menos uma operação funcionou
    const zRemSuccess = !zRemError && zRemCount >= 0;
    const delSuccess = !delError && delCount >= 0;

    if (zRemSuccess || delSuccess) {
      console.log(
        `SHOTXCRON > REDIS > REMOVE > Successfully removed message ${messageKey} (zRem: ${zRemCount}, del: ${delCount})`
      );
      return true;
    } else {
      console.error(
        `SHOTXCRON > REDIS > REMOVE > Failed to remove message ${messageKey} (zRem: ${zRemCount}, del: ${delCount})`
      );
      return false;
    }
  } catch (error) {
    console.error(
      `SHOTXCRON > REDIS > REMOVE > Error removing message ${messageKey}:`,
      error.message
    );
    console.error(error.stack);
    return false;
  }
};

/**
 * Remove todas as mensagens do Redis
 * @param {string} listKey - Chave da lista ordenada (default: 'shotx:scheduled_messages')
 * @returns {Promise<number>} - Número de mensagens removidas
 */
const removeAllMessages = async (listKey = "shotx:scheduled_messages") => {
  try {
    const client = await getRedisClient();
    if (!client) {
      console.error(
        "SHOTXCRON > REDIS > Client not available for removing messages"
      );
      return 0;
    }

    // Obter todas as chaves de mensagens
    const messageKeys = await client.zRange(listKey, 0, -1);

    if (!messageKeys || messageKeys.length === 0) {
      console.log("SHOTXCRON > REDIS > No messages to remove");
      return 0;
    }

    console.log(`SHOTXCRON > REDIS > Removing ${messageKeys.length} messages`);

    // Remover todas as mensagens em lote
    if (messageKeys.length > 0) {
      // Remover as chaves da lista ordenada
      await client.zRem(listKey, messageKeys);

      // Remover o conteúdo de cada mensagem
      await client.del(messageKeys);
    }

    console.log(
      `EMAILCRON > REDIS > Successfully removed ${messageKeys.length} messages`
    );
    return messageKeys.length;
  } catch (error) {
    console.error(
      "SHOTXCRON > REDIS > Error removing all messages:",
      error.message
    );
    return 0;
  }
};

/**
 * Diagnostica a integridade do Redis verificando chaves órfãs
 * @param {string} listKey - Chave da lista ordenada
 * @returns {Promise<Object>} - Relatório de diagnóstico
 */
const diagnoseRedisIntegrity = async (listKey = "shotx:scheduled_messages") => {
  try {
    const client = await getRedisClient();
    if (!client) {
      return { error: "Redis client not available" };
    }

    console.log(
      `SHOTXCRON > DIAGNOSE > Starting integrity check for ${listKey}`
    );

    // Obter todas as chaves do sorted set
    const sortedSetKeys = await client.zRange(listKey, 0, -1);

    if (!sortedSetKeys || sortedSetKeys.length === 0) {
      return {
        success: true,
        totalKeys: 0,
        validKeys: 0,
        orphanedKeys: [],
        missingContent: [],
        summary: "No keys found in sorted set",
      };
    }

    console.log(
      `SHOTXCRON > DIAGNOSE > Found ${sortedSetKeys.length} keys in sorted set`
    );

    // Verificar quais chaves têm conteúdo
    const pipeline = client.multi();
    sortedSetKeys.forEach((key) => {
      pipeline.exists(key);
    });

    const existsResults = await pipeline.exec();

    const validKeys = [];
    const orphanedKeys = [];

    for (let i = 0; i < sortedSetKeys.length; i++) {
      const key = sortedSetKeys[i];
      const exists = existsResults[i] && existsResults[i][1] === 1;

      if (exists) {
        validKeys.push(key);
      } else {
        orphanedKeys.push(key);
      }
    }

    console.log(
      `SHOTXCRON > DIAGNOSE > Valid keys: ${validKeys.length}, Orphaned keys: ${orphanedKeys.length}`
    );

    // Limpar chaves órfãs se encontradas
    if (orphanedKeys.length > 0) {
      console.log(
        `SHOTXCRON > DIAGNOSE > Cleaning up ${orphanedKeys.length} orphaned keys`
      );
      await client.zRem(listKey, orphanedKeys);
    }

    return {
      success: true,
      totalKeys: sortedSetKeys.length,
      validKeys: validKeys.length,
      orphanedKeys: orphanedKeys.length,
      orphanedKeysList: orphanedKeys,
      cleanedUp: orphanedKeys.length > 0,
      summary: `Found ${validKeys.length} valid and ${orphanedKeys.length} orphaned keys`,
    };
  } catch (error) {
    console.error(
      "SHOTXCRON > DIAGNOSE > Error during integrity check:",
      error.message
    );
    return {
      success: false,
      error: error.message,
      summary: "Integrity check failed",
    };
  }
};

/**
 * Força a limpeza de chaves órfãs em uma lista ordenada
 * @param {string} listKey - Chave da lista ordenada
 * @returns {Promise<number>} - Número de chaves órfãs removidas
 */
const cleanupOrphanedKeys = async (listKey = "shotx:scheduled_messages") => {
  try {
    const diagnosis = await diagnoseRedisIntegrity(listKey);

    if (diagnosis.success && diagnosis.orphanedKeys > 0) {
      console.log(
        `SHOTXCRON > CLEANUP > Removed ${diagnosis.orphanedKeys} orphaned keys from ${listKey}`
      );
      return diagnosis.orphanedKeys;
    }

    return 0;
  } catch (error) {
    console.error("SHOTXCRON > CLEANUP > Error during cleanup:", error.message);
    return 0;
  }
};

/**
 * Verifica se ainda existem mensagens pendentes no Redis para um email específico
 * @param {string} messageID - ID do email a ser verificado
 * @param {string} accountId - ID da conta associada ao email
 * @returns {Promise<boolean>} - true se existem mensagens pendentes, false caso contrário
 */
const checkPendingMessages = async (messageID, accountId) => {
  try {
    console.log(
      `EMAILCRON > CHECK PENDING > Verificando mensagens pendentes para email ${messageID}, account ${accountId}`
    );

    // Obter cliente Redis
    const client = await getRedisClient();
    if (!client) {
      console.error("EMAILCRON > CHECK PENDING > Redis client not available");
      return false;
    }

    // Validar parâmetros
    if (!messageID) {
      console.error("EMAILCRON > CHECK PENDING > Missing messageID parameter");
      return false;
    }

    if (!accountId) {
      console.error("EMAILCRON > CHECK PENDING > Missing accountId parameter");
      return false;
    }

    const listKey = "email:scheduled_emails";

    // Calcular janela de tempo: agora até 5 minutos no futuro
    const now = Date.now();
    const fiveMinutesFromNow = now + 5 * 60 * 1000; // 5 minutos em milissegundos

    // console.log(
    //   `EMAILCRON > CHECK PENDING > Buscando mensagens na janela de tempo:`,
    //   {
    //     from: new Date(now).toISOString(),
    //     to: new Date(fiveMinutesFromNow).toISOString(),
    //     windowMinutes: 5,
    //   }
    // );

    // Obter apenas mensagens agendadas para os próximos 5 minutos
    // Usar timestamps específicos ao invés de -inf/+inf para melhor performance
    const allMessageKeys = await client.zRange(
      listKey,
      now,
      fiveMinutesFromNow,
      {
        BY: "SCORE",
      }
    );

    if (!allMessageKeys || allMessageKeys.length === 0) {
      console.log(
        "EMAILCRON > CHECK PENDING > Nenhuma mensagem agendada encontrada na janela de tempo especificada"
      );
      return false;
    }

    console.log(
      `EMAILCRON > CHECK PENDING > Encontradas ${allMessageKeys.length} mensagens agendadas na janela de 5 minutos`
    );

    // Obter as mensagens em lote usando pipeline para melhor performance
    const pipeline = client.multi();

    // Adicionar comandos GET para cada chave
    allMessageKeys.forEach((key) => {
      pipeline.get(key);
    });

    // Executar pipeline
    const messageResults = await pipeline.exec();

    // Processar resultados e verificar se existe pelo menos uma mensagem correspondente
    // Usar método funcional some() que para na primeira ocorrência encontrada
    const hasPendingMessage = messageResults.some((messageStr, index) => {
      const key = allMessageKeys[index];

      if (messageStr) {
        try {
          const message = JSON.parse(messageStr);

          // Verificar se a mensagem corresponde ao messageID e accountId
          if (message.ID === messageID && message.accountId === accountId) {
            console.log(
              `EMAILCRON > CHECK PENDING > Encontrada mensagem pendente para email ${messageID}`,
              {
                key: key,
                index: message.index || "unknown",
                to: message.to || "unknown",
                score: message.score || "unknown",
              }
            );
            // Retornar true para some() parar na primeira mensagem encontrada
            return true;
          }
        } catch (e) {
          console.error(
            `EMAILCRON > CHECK PENDING > Erro ao parsear mensagem com chave ${key}:`,
            e.message
          );
          // Retornar false para continuar processamento mesmo com erro de parsing
        }
      }

      // Retornar false para continuar a busca
      return false;
    });

    if (hasPendingMessage) {
      return true;
    }

    // Se chegou até aqui, não encontrou nenhuma mensagem pendente
    console.log(
      `EMAILCRON > CHECK PENDING > Nenhuma mensagem pendente encontrada para email ${messageID}`
    );

    return false;
  } catch (error) {
    console.error(
      `EMAILCRON > CHECK PENDING > Erro ao verificar mensagens pendentes para email ${messageID}:`,
      error.message
    );
    // Em caso de erro, assumir que existem mensagens pendentes para ser conservador
    return true;
  }
};

module.exports = {
  getRedisClient,
  saveMessage,
  saveScheduledMessage,
  getScheduledMessages,
  removeMessage,
  removeAllMessages,
  diagnoseRedisIntegrity,
  cleanupOrphanedKeys,
  checkPendingMessages,
};
