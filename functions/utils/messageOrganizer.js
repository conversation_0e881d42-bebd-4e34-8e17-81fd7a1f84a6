/**
 * Utilitário para organizar instâncias ou mensagens
 * Separado para evitar dependências circulares entre shotx e shotxOrganize
 */

/**
 * Organiza instâncias ou mensagens para distribuição entre leads
 * @param {string} type - Tipo de organização: "instances" ou "messages"
 * @param {Array} instancesOrMessages - Array de instâncias ou mensagens
 * @param {number} qtdLeads - Quantidade de leads para distribuir
 * @param {boolean} random - Se deve embaralhar aleatoriamente
 * @returns {Array} Array organizado de instâncias ou mensagens
 */
const ordeneInstancesOrMessages = (
  type,
  instancesOrMessages,
  qtdLeads,
  random = false
) => {
  // Validações necessárias
  if (type !== "instances" && type !== "messages") {
    throw new Error("Parâmetro 'type' deve ser 'instances' ou 'messages'");
  }

  if (!Array.isArray(instancesOrMessages)) {
    throw new Error("Parâmetro 'instancesOrMessages' deve ser um array válido");
  }

  if (!Number.isInteger(qtdLeads) || qtdLeads <= 0) {
    throw new Error("Parâmetro 'qtdLeads' deve ser um número inteiro positivo");
  }

  // Se o array está vazio, retorna array vazio
  if (instancesOrMessages.length === 0) {
    console.log("SHOTXCRON > PREPARE > EMPTY ARRAY");
    return [];
  }

  let workingArray = [...instancesOrMessages]; // Cópia para não modificar o original

  // Se random = true, embaralhar o array
  if (random) {
    // Algoritmo Fisher-Yates para embaralhamento
    for (let i = workingArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [workingArray[i], workingArray[j]] = [workingArray[j], workingArray[i]];
    }
  }

  const result = [];

  // Se precisamos de mais elementos do que temos, repetir o array
  while (result.length < qtdLeads) {
    const remainingNeeded = qtdLeads - result.length;
    const elementsToTake = Math.min(remainingNeeded, workingArray.length);

    // Adicionar elementos do array (objetos com id e accountId)
    for (let i = 0; i < elementsToTake; i++) {
      const element = workingArray[i];
      // Extrair ID do elemento (tanto instances quanto messages usam 'id' ou 'ID')
      const id = element.id || element.ID;
      if (type === "messages") {
        const message = element.message;
        console.log("SHOTXCRON > PREPARE > MESSAGE", message);
        result.push({
          id: id,
          accountId: element.accountId,
          message: element.message,
        });
      } else {
        result.push({
          id: id,
          accountId: element.accountId,
        });
      }
    }

    // Se ainda precisamos de mais elementos, embaralhar novamente se random = true
    if (result.length < qtdLeads && random) {
      for (let i = workingArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [workingArray[i], workingArray[j]] = [workingArray[j], workingArray[i]];
      }
    }
  }

  return result;
};

module.exports = {
  ordeneInstancesOrMessages,
};
